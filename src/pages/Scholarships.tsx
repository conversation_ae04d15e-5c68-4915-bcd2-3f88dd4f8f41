import React, { useState, useEffect } from 'react';
import { Pagination, Spin, Alert } from 'antd';
import { useLocation } from 'react-router-dom';
import EnhancedScholarshipCard from '../components/EnhancedScholarshipCard';

import PageEndSuggestions from '../components/PageEndSuggestions';
import AdPlacement from '../components/AdPlacement';
import StandardizedFilters from '../components/filters/StandardizedFilters';

interface Scholarship {
  id: number;
  title: string;
  description: string;
  level: string;
  country: string;
  deadline: string;
  isOpen: boolean;
  thumbnail: string;
}

interface PaginationData {
  total: number;
  page: number;
  limit: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

const Scholarships: React.FC = () => {
  const location = useLocation();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedLevel, setSelectedLevel] = useState('');
  const [selectedCountry, setSelectedCountry] = useState('');
  const [selectedStatus, setSelectedStatus] = useState('');
  const [scholarships, setScholarships] = useState<Scholarship[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState<PaginationData>({
    total: 0,
    page: 1,
    limit: 9, // Show 9 scholarships per page (3x3 grid)
    totalPages: 0,
    hasNextPage: false,
    hasPreviousPage: false
  });

  // Read URL parameters on component mount
  useEffect(() => {
    const searchParams = new URLSearchParams(location.search);
    const levelParam = searchParams.get('level');
    const countryParam = searchParams.get('country');

    if (levelParam) {
      setSelectedLevel(levelParam);
    }
    if (countryParam) {
      setSelectedCountry(countryParam);
    }
  }, [location.search]);

  // Fetch scholarships with pagination and filters
  useEffect(() => {
    fetchScholarships();
  }, [pagination.page, selectedLevel, selectedCountry, searchQuery]);

  const fetchScholarships = async () => {
    try {
      setLoading(true);

      // Build query parameters
      const params = new URLSearchParams();
      params.append('page', pagination.page.toString());
      params.append('limit', pagination.limit.toString());

      if (searchQuery) {
        params.append('q', searchQuery);
      }

      if (selectedLevel) {
        params.append('level', selectedLevel);
      }

      if (selectedCountry) {
        params.append('country', selectedCountry);
      }

      const response = await fetch(`${process.env.REACT_APP_API_URL || 'http://localhost:5000'}/api/scholarships/search?${params.toString()}`);

      if (!response.ok) {
        throw new Error('Failed to fetch scholarships');
      }

      const data = await response.json();
      console.log('Scholarships search API response:', data);

      // Handle the correct API response format: { success: true, data: [...], pagination: {...} }
      const scholarshipsData = data.data || data.scholarships || [];
      const paginationData = data.pagination || {};

      setScholarships(scholarshipsData);
      setPagination(paginationData || {
        total: scholarshipsData.length || 0,
        page: 1,
        limit: 9,
        totalPages: Math.ceil((scholarshipsData.length || 0) / 9),
        hasNextPage: false,
        hasPreviousPage: false
      });
      setError(null);
    } catch (err) {
      console.error('Error fetching scholarships:', err);
      setError('Failed to load scholarships. Please try again later.');
      setScholarships([]);
    } finally {
      setLoading(false);
    }
  };

  // Handle page change
  const handlePageChange = (page: number) => {
    setPagination(prev => ({
      ...prev,
      page
    }));
    // Scroll to top when page changes
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  return (
    <div className="bg-white min-h-screen">
      {/* Hero Section - Main Title Only */}
      <div className="relative bg-gradient-to-br from-blue-900 via-primary-dark to-primary overflow-hidden">
        {/* Background overlay */}
        <div className="absolute inset-0 bg-gradient-to-r from-primary/80 to-primary-dark/80 mix-blend-multiply" />

        <div className="max-w-7xl mx-auto pt-20 pb-3 px-4 sm:pt-24 sm:pb-4 sm:px-6 lg:px-8 relative z-10">
          <div className="text-center">
            <div className="flex items-center justify-center mb-1">
              <div className="flex items-center justify-center w-6 h-6 bg-white/20 rounded-lg mr-2">
                <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                </svg>
              </div>
              <h1 className="text-lg font-medium text-white sm:text-xl lg:text-2xl animate-fade-in">
                Toutes les Bourses d'Études Disponibles
              </h1>
            </div>
            <p className="text-sm text-blue-100 max-w-2xl mx-auto animate-fade-in leading-relaxed">
              Explorez notre collection complète d'opportunités de financement pour tous les niveaux d'études
            </p>
          </div>
        </div>
      </div>

      {/* Content Section - Professional Format */}
      <div className="bg-gray-100 py-4 md:py-6">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <article>
            {/* Article Content - Full Width */}
            <div className="prose max-w-none">
              <p className="text-gray-600 leading-relaxed mb-3 text-xs md:text-sm text-justify">
                Bienvenue sur la plateforme de référence pour les bourses d'études internationales. Cette page centralise toutes les opportunités de financement disponibles, soigneusement vérifiées et mises à jour quotidiennement pour vous offrir les meilleures chances de réussite dans vos projets académiques.
              </p>

              <p className="text-gray-600 leading-relaxed mb-3 text-xs md:text-sm text-justify">
                Vous trouverez ici des bourses pour tous les niveaux d'études - de la licence au doctorat - proposées par des gouvernements, des universités prestigieuses, des fondations internationales et des organisations privées. Chaque opportunité est accompagnée d'informations détaillées sur les critères d'éligibilité, les avantages financiers, les procédures de candidature et les échéances importantes.
              </p>

              <p className="text-gray-600 leading-relaxed text-xs md:text-sm text-justify">
                Notre système de recherche intelligent vous permettra de découvrir rapidement les bourses qui correspondent parfaitement à votre profil et à vos ambitions académiques.
              </p>
            </div>
          </article>
        </div>
      </div>

      {/* Ad Placement - Top Banner */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 -mt-4 mb-6 hidden md:block">
        <AdPlacement
          adSlot="1234567890"
          adSize="leaderboard"
          responsive={true}
          fullWidth={true}
        />
      </div>



      {/* Content Section */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pb-16">
        {/* Main Content - Full Width */}
        <div className="w-full">
        {/* Results header */}
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
          <div>
            <h2 className="text-2xl font-bold text-gray-900">Résultats de recherche</h2>
          </div>
          <div className="mt-4 md:mt-0">
            <select
              className="rounded-xl border border-gray-300 bg-white py-2 px-3 shadow-sm focus:border-primary focus:outline-none focus:ring-primary text-sm"
            >
              <option value="recent">Plus récentes</option>
              <option value="deadline">Date limite proche</option>
              <option value="relevance">Pertinence</option>
            </select>
          </div>
        </div>

        {loading ? (
          <div className="flex justify-center items-center py-16">
            <Spin size="large" tip="Chargement des bourses..." />
          </div>
        ) : error ? (
          <Alert
            message="Erreur"
            description={error}
            type="error"
            showIcon
            className="mb-6 rounded-xl shadow-md"
          />
        ) : (
          <>
            {/* Mobile Ad - Only visible on small screens */}
            <div className="mb-8 md:hidden">
              <AdPlacement
                adSlot="2345678901"
                adSize="rectangle"
                responsive={true}
                fullWidth={true}
              />
            </div>

            <div className="grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3">
              {scholarships.map((scholarship, index) => (
                <div key={scholarship.id} className="animate-fade-in" style={{ animationDelay: `${index * 0.1}s` }}>
                  <EnhancedScholarshipCard
                    id={scholarship.id}
                    title={scholarship.title}
                    thumbnail={scholarship.thumbnail}
                    deadline={scholarship.deadline}
                    isOpen={scholarship.isOpen}
                    level={scholarship.level}
                    country={scholarship.country}
                    onClick={(id, slug) => window.location.href = slug ? `/bourse/${slug}` : `/scholarships/${id}`}
                    index={index}
                  />
                </div>
              ))}
            </div>

            {/* No Results Message */}
            {scholarships.length === 0 && (
              <div className="text-center py-16 bg-gray-50 rounded-2xl shadow-sm border border-gray-100">
                <svg className="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <h3 className="mt-4 text-lg font-medium text-gray-900">Aucune bourse trouvée</h3>
                <p className="mt-2 text-sm text-gray-500 max-w-md mx-auto">
                  Essayez d'ajuster vos critères de recherche ou de filtrage pour trouver ce que vous cherchez.
                </p>
                <button
                  onClick={() => {
                    setSearchQuery('');
                    setSelectedLevel('');
                    setSelectedCountry('');
                    setPagination(prev => ({ ...prev, page: 1 }));
                  }}
                  className="mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                >
                  Réinitialiser les filtres
                </button>
              </div>
            )}

            {/* Pagination */}
            {pagination.total > 0 && (
              <div className="flex justify-center mt-6">
                <Pagination
                  current={pagination.page}
                  total={pagination.total}
                  pageSize={pagination.limit}
                  onChange={handlePageChange}
                  showSizeChanger={false}
                  showQuickJumper
                  showTotal={() => null}
                  className="shadow-sm rounded-xl p-2 bg-white"
                />
              </div>
            )}
          </>
        )}
        </div>
      </div>

        {/* Page End Suggestions */}
        <PageEndSuggestions
          currentPageType="scholarship"
          currentItem={selectedLevel || selectedCountry}
        />
      </div>
    );
};

export default Scholarships;