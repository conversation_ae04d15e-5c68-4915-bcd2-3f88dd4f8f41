import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useLanguage } from '../context/LanguageContext';
import { constructImageUrl } from '../utils/imageUtils';

interface SuggestionItem {
  id: number;
  title: string;
  thumbnail?: string;
  deadline?: string;
  isOpen?: boolean;
  country?: string;
  level?: string;
  type?: string;
  description?: string;
  slug?: string;
}

interface PageEndSuggestionsProps {
  currentPageType: 'scholarship' | 'country' | 'level' | 'opportunity';
  currentItem?: string;
  excludeId?: number;
  className?: string;
}

const PageEndSuggestions: React.FC<PageEndSuggestionsProps> = ({
  currentPageType,
  currentItem,
  excludeId,
  className = ''
}) => {
  const { translations } = useLanguage();
  const [suggestions, setSuggestions] = useState<SuggestionItem[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchSuggestions();
  }, [currentPageType, currentItem, excludeId]);

  const fetchSuggestions = async () => {
    try {
      setLoading(true);
      const apiUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';

      let endpoint = '';
      const params = new URLSearchParams();
      params.append('limit', '6'); // 3x2 layout = 6 items

      if (excludeId) {
        params.append('excludeId', excludeId.toString());
      }

      switch (currentPageType) {
        case 'scholarship':
          endpoint = `${apiUrl}/api/scholarships/latest`;
          break;
        case 'country':
          endpoint = `${apiUrl}/api/scholarships/latest`;
          if (currentItem) {
            params.append('excludeCountry', currentItem);
          }
          break;
        case 'level':
          endpoint = `${apiUrl}/api/scholarships/latest`;
          if (currentItem) {
            params.append('excludeLevel', currentItem);
          }
          break;
        case 'opportunity':
          endpoint = `${apiUrl}/api/opportunities/latest`;
          break;
        default:
          endpoint = `${apiUrl}/api/scholarships/latest`;
      }

      const response = await fetch(`${endpoint}?${params.toString()}`);
      if (response.ok) {
        const data = await response.json();
        setSuggestions(data.data || data.scholarships || []);
      }
    } catch (error) {
      console.error('Error fetching suggestions:', error);
    } finally {
      setLoading(false);
    }
  };

  const getItemLink = (item: SuggestionItem) => {
    switch (currentPageType) {
      case 'opportunity':
        return `/opportunities/${item.id}`;
      default:
        if (item.slug) {
          return `/bourse/${item.slug}`;
        }
        return `/scholarships/${item.id}`;
    }
  };

  const getSectionTitle = () => {
    switch (currentPageType) {
      case 'scholarship':
        return 'Bourses Recommandées';
      case 'country':
        return 'Bourses Recommandées';
      case 'level':
        return 'Bourses Recommandées';
      case 'opportunity':
        return 'Opportunités Recommandées';
      default:
        return 'Recommandations';
    }
  };

  if (loading || suggestions.length === 0) {
    return null;
  }

  return (
    <div className={`bg-gradient-to-br from-gray-50 to-blue-50 py-6 ${className}`}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-6">
          <h2 className="text-2xl font-bold text-gray-900">
            {getSectionTitle()}
          </h2>
        </div>

        {/* Professional 3x2 Card Layout */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 max-w-6xl mx-auto">
          {suggestions.slice(0, 6).map((item, index) => (
            <Link
              key={item.id}
              to={getItemLink(item)}
              className="group bg-white rounded-xl shadow-md hover:shadow-xl transition-all duration-300 overflow-hidden border border-gray-100 hover:border-blue-200 transform hover:-translate-y-2"
              style={{ animationDelay: `${index * 0.1}s` }}
            >
              {/* Thumbnail */}
              <div className="relative h-36 bg-gradient-to-br from-blue-500 to-indigo-600 overflow-hidden">
                {item.thumbnail ? (
                  <img
                    src={constructImageUrl(item.thumbnail, 'card')}
                    alt={item.title}
                    className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                    onError={(e) => {
                      const target = e.target as HTMLImageElement;
                      target.src = constructImageUrl(null);
                    }}
                  />
                ) : (
                  <div className="w-full h-full flex items-center justify-center">
                    <div className="text-white text-6xl opacity-20">
                      {currentPageType === 'opportunity' ? '🚀' : '🎓'}
                    </div>
                  </div>
                )}
                
                {/* Status Badge */}
                {item.isOpen !== undefined && (
                  <div className="absolute top-4 right-4">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                      item.isOpen 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-red-100 text-red-800'
                    }`}>
                      <span className={`w-1.5 h-1.5 rounded-full mr-1 ${
                        item.isOpen ? 'bg-green-400' : 'bg-red-400'
                      }`}></span>
                      {item.isOpen ? 'Ouvert' : 'Fermé'}
                    </span>
                  </div>
                )}

                {/* Country/Level Badge */}
                {(item.country || item.level) && (
                  <div className="absolute bottom-4 left-4">
                    <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-white/90 text-gray-800 backdrop-blur-sm">
                      {item.country || item.level}
                    </span>
                  </div>
                )}
              </div>

              {/* Content - Compact for 3x2 Layout */}
              <div className="p-4">
                <h3 className="text-base font-semibold text-gray-900 mb-2 line-clamp-2 group-hover:text-blue-600 transition-colors duration-200">
                  {item.title}
                </h3>

                {/* Deadline */}
                {item.deadline && (
                  <div className="flex items-center text-xs text-gray-500">
                    <svg className="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                    {new Date(item.deadline).toLocaleDateString('fr-FR')}
                  </div>
                )}

                {/* Call to Action */}
                <div className="flex items-center text-blue-600 text-sm font-medium group-hover:text-blue-700">
                  <span>Voir les détails</span>
                  <svg className="ml-1 h-4 w-4 group-hover:translate-x-1 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </div>
              </div>
            </Link>
          ))}
        </div>

        {/* Call to Action */}
        <div className="text-center mt-12">
          <Link
            to="/bourses"
            className="inline-flex items-center px-8 py-3 border border-transparent text-base font-medium rounded-xl text-white bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 transform hover:scale-105 transition-all duration-200 shadow-lg hover:shadow-xl"
          >
            <span>Voir Toutes les Bourses</span>
            <svg className="ml-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
            </svg>
          </Link>
        </div>
      </div>
    </div>
  );
};

export default PageEndSuggestions;
