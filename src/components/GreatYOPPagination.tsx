import React from 'react';

interface GreatYOPPaginationProps {
  current: number;
  total: number;
  pageSize: number;
  onChange: (page: number) => void;
  showQuickJumper?: boolean;
  className?: string;
}

const GreatYOPPagination: React.FC<GreatYOPPaginationProps> = ({
  current,
  total,
  pageSize,
  onChange,
  showQuickJumper = false,
  className = '',
}) => {
  const totalPages = Math.ceil(total / pageSize);
  
  if (totalPages <= 1) {
    return null;
  }

  const getVisiblePages = () => {
    const pages: (number | string)[] = [];
    
    // Always show first page
    pages.push(1);
    
    // Show current page and surrounding pages
    const start = Math.max(2, current - 1);
    const end = Math.min(totalPages - 1, current + 1);
    
    // Add dots if there's a gap after page 1
    if (start > 2) {
      pages.push('...');
    }
    
    // Add pages around current page
    for (let i = start; i <= end; i++) {
      if (i !== 1 && i !== totalPages) {
        pages.push(i);
      }
    }
    
    // Add dots if there's a gap before last page
    if (end < totalPages - 1) {
      pages.push('...');
    }
    
    // Always show last page (if it's not the first page)
    if (totalPages > 1) {
      pages.push(totalPages);
    }
    
    return pages;
  };

  const visiblePages = getVisiblePages();

  const handlePageClick = (page: number | string) => {
    if (typeof page === 'number' && page !== current) {
      onChange(page);
    }
  };

  const handlePrevious = () => {
    if (current > 1) {
      onChange(current - 1);
    }
  };

  const handleNext = () => {
    if (current < totalPages) {
      onChange(current + 1);
    }
  };

  return (
    <div className={`gy-paginat-position ${className}`}>
      <nav className="navigation pagination" aria-label="Pagination Navigation">
        <h2 className="screen-reader-text sr-only">Pagination Navigation</h2>
        <div className="nav-links">
          {/* Previous button */}
          {current > 1 && (
            <button
              onClick={handlePrevious}
              className="page-numbers prev-page"
              aria-label="Previous page"
            >
              <i className="fa fa-caret-left" aria-hidden="true"></i>
            </button>
          )}

          {/* Page numbers */}
          {visiblePages.map((page, index) => {
            if (page === '...') {
              return (
                <span key={`dots-${index}`} className="page-numbers dots">
                  …
                </span>
              );
            }

            const pageNumber = page as number;
            const isCurrent = pageNumber === current;

            return (
              <button
                key={pageNumber}
                onClick={() => handlePageClick(pageNumber)}
                className={`page-numbers ${isCurrent ? 'current' : ''}`}
                aria-current={isCurrent ? 'page' : undefined}
                aria-label={isCurrent ? `Current page ${pageNumber}` : `Go to page ${pageNumber}`}
              >
                {pageNumber}
              </button>
            );
          })}

          {/* Next button */}
          {current < totalPages && (
            <button
              onClick={handleNext}
              className="page-numbers next-page"
              aria-label="Next page"
            >
              <i className="fa fa-caret-right" aria-hidden="true"></i>
            </button>
          )}
        </div>
      </nav>
    </div>
  );
};

export default GreatYOPPagination;
